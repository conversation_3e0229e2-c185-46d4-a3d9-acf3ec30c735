# DevContainer Setup Scripts

This directory contains setup scripts for the Sentry Ruby development environment.

## Main Setup Script

### `setup` / `setup.rb`

The main setup script that installs dependencies for the Sentry Ruby workspace. It has been rewritten in Ruby for better maintainability.

#### Usage

```bash
# Run full setup (default behavior)
.devcontainer/setup

# Show help
.devcontainer/setup --help

# Only process specific folders
.devcontainer/setup --only sentry-ruby,sentry-rails

# Only run bundle operations (skip npm)
.devcontainer/setup --only-bundle

# Only run npm operations (skip bundle)
.devcontainer/setup --only-npm

# Combine options for mini services
.devcontainer/setup --only sentry-ruby,sentry-rails --only-bundle
.devcontainer/setup --only-npm
```

#### Options

- `--only FOLDERS`: Only process specified folders (comma-separated list)
- `--only-bundle`: Only run bundle operations, skip npm installation
- `--only-npm`: Only run npm operations, skip bundle installation
- `--help`: Show help message

#### What it does

**Bundle operations** (when not using `--only-npm`):
1. Cleans up `.ruby-lsp` directories
2. Runs `bundle install` for specified workspace folders
3. Installs the `foreman` gem

**NPM operations** (when not using `--only-bundle`):
1. Runs `npm install` for the Svelte mini app in `spec/apps/svelte-mini`

#### Workspace Folders

The script processes these folders by default:
- `sentry-ruby`
- `sentry-rails`
- `sentry-sidekiq`
- `sentry-delayed_job`
- `sentry-resque`
- `sentry-opentelemetry`

## Mini Service Entrypoints

### `entrypoint-rails-mini.rb`

Entrypoint script for the sentry-rails-mini service. Runs setup with only `sentry-ruby` and `sentry-rails` folders, bundle operations only.

```bash
.devcontainer/entrypoint-rails-mini.rb
```

Equivalent to:
```bash
.devcontainer/setup --only sentry-ruby,sentry-rails --only-bundle
```

### `entrypoint-svelte-mini.rb`

Entrypoint script for the sentry-svelte-mini service. Runs setup with npm operations only.

```bash
.devcontainer/entrypoint-svelte-mini.rb
```

Equivalent to:
```bash
.devcontainer/setup --only-npm
```

## Backward Compatibility

The original bash script has been backed up as `setup.sh.bak`. The new `setup` script is a wrapper that calls `setup.rb`, maintaining full backward compatibility with existing configurations.

## Development

The Ruby implementation provides:
- Better error handling and reporting
- More maintainable code structure
- Flexible option parsing
- Spinner animations for long-running operations
- Clear separation of concerns between bundle and npm operations
