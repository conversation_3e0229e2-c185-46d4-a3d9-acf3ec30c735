#!/bin/bash

# Exit on any error
set -e

echo "🚀 Running post-create setup script..."

cd /workspace/sentry

# Spinner function
show_spinner() {
  local pid=$1
  local message=$2
  local spin='-\|/'
  local i=0

  echo -n "$message "
  while kill -0 $pid 2>/dev/null; do
    i=$(( (i+1) %4 ))
    printf "\r$message ${spin:$i:1}"
    sleep 0.1
  done
  printf "\r$message ✅\n"
}

WORKSPACE_FOLDERS=(
  "sentry-ruby"
  "sentry-rails"
  "sentry-sidekiq"
  "sentry-delayed_job"
  "sentry-resque"
  "sentry-opentelemetry"
)

ALL_FOLDERS=("." "${WORKSPACE_FOLDERS[@]}")

echo "🧹 Cleaning up .ruby-lsp directories..."
for folder in "${ALL_FOLDERS[@]}"; do
  if [ -d "$folder/.ruby-lsp" ]; then
    echo "  Removing $folder/.ruby-lsp"
    rm -rf "$folder/.ruby-lsp"
  fi
done

echo "📦 Installing bundle dependencies for workspace folders..."
for folder in "${WORKSPACE_FOLDERS[@]}"; do
  if [ -d "$folder" ] && [ -f "$folder/Gemfile" ]; then
    cd "/workspace/sentry/$folder"

    bundle install > /dev/null 2>&1 &
    bundle_pid=$!

    show_spinner $bundle_pid "   $folder"

    wait $bundle_pid
    bundle_exit_code=$?

    if [ $bundle_exit_code -ne 0 ]; then
      echo "  ❌ Bundle install failed in $folder"
      echo "     Please check for missing system dependencies or run bundle install manually"
      exit 1
    fi

    cd /workspace/sentry
  else
    echo "  Skipping $folder (no Gemfile found or directory doesn't exist)"
  fi
done

gem install foreman > /dev/null 2>&1 &
foreman_pid=$!
show_spinner $foreman_pid "💎 Installing foreman gem"
wait $foreman_pid
foreman_exit_code=$?

if [ $foreman_exit_code -ne 0 ]; then
  echo "❌ Failed to install foreman gem"
  exit 1
fi

cd /workspace/sentry/spec/apps/svelte-mini

npm install > /dev/null 2>&1 &
npm_pid=$!
show_spinner $npm_pid "📦 Installing npm dependencies for e2e tests"
wait $npm_pid
npm_exit_code=$?

if [ $npm_exit_code -ne 0 ]; then
  echo "❌ Failed to install npm dependencies for e2e tests"
  exit 1
fi

echo "✅ Post-create setup completed!"
